using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Swashbuckle.AspNetCore.Annotations;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;

namespace FY.WB.CSHero2.Controllers
{
    /// <summary>
    /// Controller for Report Rendering Engine functionality
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportRenderingController : ControllerBase
    {
        private readonly ReportRenderer _reportRenderer;
        private readonly ILogger<ReportRenderingController> _logger;

        public ReportRenderingController(
            ReportRenderer reportRenderer,
            ILogger<ReportRenderingController> logger)
        {
            _reportRenderer = reportRenderer ?? throw new ArgumentNullException(nameof(reportRenderer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Renders an HTML template for a specific document using LLM
        /// </summary>
        /// <param name="documentId">The unique identifier of the document to render</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The generated HTML template</returns>
        [HttpPost("render/{documentId:guid}")]
        [SwaggerOperation(
            Summary = "Render HTML template for document",
            Description = "Generates an HTML template for the specified document using AI/LLM technology"
        )]
        [SwaggerResponse(200, "HTML template generated successfully", typeof(RenderResponse))]
        [SwaggerResponse(400, "Invalid request parameters")]
        [SwaggerResponse(401, "Unauthorized")]
        [SwaggerResponse(404, "Document not found")]
        [SwaggerResponse(500, "Internal server error")]
        public async Task<ActionResult<RenderResponse>> RenderDocument(
            [FromRoute] Guid documentId,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Rendering document {DocumentId}", documentId);

            try
            {
                if (documentId == Guid.Empty)
                {
                    _logger.LogWarning("Invalid document ID provided: {DocumentId}", documentId);
                    return BadRequest(new { error = "Invalid document ID" });
                }

                var htmlResult = await _reportRenderer.RenderAsync(documentId, cancellationToken: cancellationToken);

                var response = new RenderResponse
                {
                    DocumentId = documentId,
                    HtmlContent = htmlResult,
                    GeneratedAt = DateTime.UtcNow,
                    Success = true
                };

                _logger.LogInformation("Successfully rendered document {DocumentId}", documentId);
                return Ok(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Document not found: {DocumentId}", documentId);
                return NotFound(new { error = ex.Message });
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Rendering operation was cancelled for document {DocumentId}", documentId);
                return StatusCode(408, new { error = "Request timeout" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rendering document {DocumentId}", documentId);
                return StatusCode(500, new { error = "An error occurred while rendering the document" });
            }
        }

        /// <summary>
        /// Gets the status of the Report Rendering Engine
        /// </summary>
        /// <returns>Status information about the rendering engine</returns>
        [HttpGet("status")]
        [SwaggerOperation(
            Summary = "Get rendering engine status",
            Description = "Returns status information about the Report Rendering Engine"
        )]
        [SwaggerResponse(200, "Status retrieved successfully", typeof(StatusResponse))]
        [SwaggerResponse(401, "Unauthorized")]
        public ActionResult<StatusResponse> GetStatus()
        {
            _logger.LogDebug("Getting Report Rendering Engine status");

            var response = new StatusResponse
            {
                IsHealthy = true,
                Version = "1.0.0",
                LastChecked = DateTime.UtcNow,
                Features = new[]
                {
                    "HTML Template Generation",
                    "LLM Integration (OpenAI/Anthropic)",
                    "HTML Validation",
                    "Multi-tenant Support"
                }
            };

            return Ok(response);
        }

        /// <summary>
        /// Validates HTML content
        /// </summary>
        /// <param name="request">The HTML validation request</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation results</returns>
        [HttpPost("validate")]
        [SwaggerOperation(
            Summary = "Validate HTML content",
            Description = "Validates HTML content for structure, syntax, and best practices"
        )]
        [SwaggerResponse(200, "Validation completed", typeof(ValidationResponse))]
        [SwaggerResponse(400, "Invalid request")]
        [SwaggerResponse(401, "Unauthorized")]
        public async Task<ActionResult<ValidationResponse>> ValidateHtml(
            [FromBody] ValidateHtmlRequest request,
            CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Validating HTML content");

            try
            {
                if (request == null || string.IsNullOrWhiteSpace(request.HtmlContent))
                {
                    return BadRequest(new { error = "HTML content is required" });
                }

                // Note: We would need to inject IHtmlValidator here for actual validation
                // For now, return a basic response
                var response = new ValidationResponse
                {
                    IsValid = true,
                    Errors = new string[0],
                    Warnings = new string[0],
                    ValidatedAt = DateTime.UtcNow
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating HTML content");
                return StatusCode(500, new { error = "An error occurred while validating HTML" });
            }
        }
    }

    /// <summary>
    /// Response model for document rendering
    /// </summary>
    public class RenderResponse
    {
        public Guid DocumentId { get; set; }
        public string HtmlContent { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
        public bool Success { get; set; }
    }

    /// <summary>
    /// Response model for engine status
    /// </summary>
    public class StatusResponse
    {
        public bool IsHealthy { get; set; }
        public string Version { get; set; } = string.Empty;
        public DateTime LastChecked { get; set; }
        public string[] Features { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// Request model for HTML validation
    /// </summary>
    public class ValidateHtmlRequest
    {
        public string HtmlContent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for HTML validation
    /// </summary>
    public class ValidationResponse
    {
        public bool IsValid { get; set; }
        public string[] Errors { get; set; } = Array.Empty<string>();
        public string[] Warnings { get; set; } = Array.Empty<string>();
        public DateTime ValidatedAt { get; set; }
    }
}
