<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.15" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="FY.WB.CSHero2.Infrastructure\FY.WB.CSHero2.Infrastructure.csproj" />
    <ProjectReference Include="FY.WB.CSHero2.ReportRenderingEngine.Infrastructure\FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.csproj" />
    <ProjectReference Include="FY.WB.CSHero2.Test\FY.WB.CSHero2.Test.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="FY.WB.CSHero2.Test\appsettings.Test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>