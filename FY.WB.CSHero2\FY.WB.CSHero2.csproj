<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-FY.WB.CSHero2-e26bc3df-2cee-4e40-a530-99ac129eaa8e</UserSecretsId>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Finbuckle.MultiTenant" Version="9.1.3" />
    <PackageReference Include="Finbuckle.MultiTenant.AspNetCore" Version="9.1.3" />
    <PackageReference Include="Finbuckle.MultiTenant.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.4" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.15" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.15">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="2.18.1" />
    <PackageReference Include="Microsoft.Identity.Web.DownstreamApi" Version="2.18.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FY.WB.CSHero2.Application\FY.WB.CSHero2.Application.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.Infrastructure\FY.WB.CSHero2.Infrastructure.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.ReportRenderingEngine.Infrastructure\FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.csproj" />
  </ItemGroup>

</Project>
